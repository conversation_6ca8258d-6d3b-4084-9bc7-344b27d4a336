import React, { useState, useEffect } from 'react';
import { Navigate } from 'react-router-dom';
import apiService from '../../services/apiService';
import { useAdminView } from '../../context/AdminViewContext';

const SubAdminRoute = ({ children }) => {
  const [isAuthorized, setIsAuthorized] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const { isViewingAsUser } = useAdminView();
  
  useEffect(() => {
    const checkSubAdminStatus = async () => {
      try {
        // First check if user is authenticated at all
        if (!apiService.isAuthenticated()) {
          setIsLoading(false);
          return;
        }
        
        // Get user profile to check admin/sub-admin status
        const userProfile = await apiService.getUserProfile();
        
        // Allow access if user is admin or sub-admin
        const hasAccess = userProfile.isAdmin === true || userProfile.isSubAdmin === true;
        setIsAuthorized(hasAccess);
        setIsLoading(false);
      } catch (error) {
        console.error('Error checking sub-admin status:', error);
        setIsLoading(false);
      }
    };
    
    checkSubAdminStatus();
  }, []);
  
  // Show loading state while checking authorization
  if (isLoading) {
    return <div>Checking permissions...</div>;
  }
  
  // If not authenticated or not authorized, redirect to home
  if (!apiService.isAuthenticated() || !isAuthorized) {
    console.log('User not authorized to access admin area');
    return <Navigate to="/home" replace />;
  }

  // If admin is viewing as user, redirect to home
  if (isViewingAsUser) {
    console.log('Admin is viewing as user, redirecting to home');
    return <Navigate to="/home" replace />;
  }

  // If authorized and not viewing as user, render the protected component
  return children;
};

export default SubAdminRoute;
