import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { useNavigate } from 'react-router-dom';
import Layout from '../Layout/Layout';
import './AdminDashboard.css';

function MealManagement() {
  const [meals, setMeals] = useState([]);
  const [filteredMeals, setFilteredMeals] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [activeTab, setActiveTab] = useState('meals');
  const [searchTerm, setSearchTerm] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('all');
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [selectedMeal, setSelectedMeal] = useState(null);
  const [newMeal, setNewMeal] = useState({
    name: '',
    description: '',
    category: '',
    ingredients: '',
    instructions: '',
    nutritionalInfo: {
      calories: '',
      protein: '',
      carbs: '',
      fat: ''
    },
    dietaryTags: [],
    prepTime: '',
    cookTime: '',
    servings: ''
  });
  const navigate = useNavigate();

  const categories = [
    'Breakfast', 'Lunch', 'Dinner', 'Snack', 'Dessert', 
    'Appetizer', 'Main Course', 'Side Dish', 'Beverage'
  ];

  const dietaryTags = [
    'Vegetarian', 'Vegan', 'Gluten-Free', 'Dairy-Free', 
    'Low-Carb', 'High-Protein', 'Keto', 'Paleo'
  ];

  useEffect(() => {
    fetchMeals();
  }, []);

  useEffect(() => {
    filterMeals();
  }, [meals, searchTerm, categoryFilter]);

  const fetchMeals = async () => {
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        navigate('/login');
        return;
      }

      const config = {
        headers: {
          'x-auth-token': token
        }
      };

      const response = await axios.get('http://localhost:5000/api/admin/meals', config);
      setMeals(response.data);
      setLoading(false);
    } catch (err) {
      if (err.response && err.response.status === 403) {
        setError('Access denied. Meal management privileges required.');
        navigate('/home');
      } else {
        setError('Failed to load meals. Please try again.');
        console.error('Meal fetch error:', err);
      }
      setLoading(false);
    }
  };

  const filterMeals = () => {
    let filtered = meals;

    if (searchTerm) {
      filtered = filtered.filter(meal =>
        meal.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        meal.description.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    if (categoryFilter !== 'all') {
      filtered = filtered.filter(meal => meal.category === categoryFilter);
    }

    setFilteredMeals(filtered);
  };

  const handleAddMeal = async () => {
    try {
      const token = localStorage.getItem('token');
      const config = {
        headers: {
          'x-auth-token': token,
          'Content-Type': 'application/json'
        }
      };

      const mealData = {
        ...newMeal,
        ingredients: newMeal.ingredients.split(',').map(ing => ing.trim()),
        instructions: newMeal.instructions.split('\n').filter(inst => inst.trim())
      };

      await axios.post('http://localhost:5000/api/admin/meals', mealData, config);
      
      setShowAddModal(false);
      setNewMeal({
        name: '',
        description: '',
        category: '',
        ingredients: '',
        instructions: '',
        nutritionalInfo: { calories: '', protein: '', carbs: '', fat: '' },
        dietaryTags: [],
        prepTime: '',
        cookTime: '',
        servings: ''
      });
      fetchMeals();
    } catch (err) {
      console.error('Add meal error:', err);
      setError('Failed to add meal. Please try again.');
    }
  };

  const handleEditMeal = async () => {
    try {
      const token = localStorage.getItem('token');
      const config = {
        headers: {
          'x-auth-token': token,
          'Content-Type': 'application/json'
        }
      };

      const mealData = {
        ...selectedMeal,
        ingredients: Array.isArray(selectedMeal.ingredients) 
          ? selectedMeal.ingredients 
          : selectedMeal.ingredients.split(',').map(ing => ing.trim()),
        instructions: Array.isArray(selectedMeal.instructions)
          ? selectedMeal.instructions
          : selectedMeal.instructions.split('\n').filter(inst => inst.trim())
      };

      await axios.put(`http://localhost:5000/api/admin/meals/${selectedMeal._id}`, mealData, config);
      
      setShowEditModal(false);
      setSelectedMeal(null);
      fetchMeals();
    } catch (err) {
      console.error('Edit meal error:', err);
      setError('Failed to update meal. Please try again.');
    }
  };

  const handleDeleteMeal = async (mealId) => {
    if (!window.confirm('Are you sure you want to delete this meal?')) {
      return;
    }

    try {
      const token = localStorage.getItem('token');
      const config = {
        headers: {
          'x-auth-token': token
        }
      };

      await axios.delete(`http://localhost:5000/api/admin/meals/${mealId}`, config);
      fetchMeals();
    } catch (err) {
      console.error('Delete meal error:', err);
      setError('Failed to delete meal. Please try again.');
    }
  };

  const openEditModal = (meal) => {
    setSelectedMeal({
      ...meal,
      ingredients: Array.isArray(meal.ingredients) 
        ? meal.ingredients.join(', ') 
        : meal.ingredients,
      instructions: Array.isArray(meal.instructions)
        ? meal.instructions.join('\n')
        : meal.instructions
    });
    setShowEditModal(true);
  };

  if (loading) {
    return (
      <Layout>
        <div className="admin-dashboard">
          <div>Loading meal management...</div>
        </div>
      </Layout>
    );
  }

  if (error) {
    return (
      <Layout>
        <div className="admin-dashboard">
          <div className="error-message">{error}</div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="admin-dashboard">
        <h1>Meal Management</h1>

        <div className="admin-tabs">
          <button
            className={`tab-button ${activeTab === 'meals' ? 'active' : ''}`}
            onClick={() => setActiveTab('meals')}
          >
            Meals
          </button>
          <button
            className={`tab-button ${activeTab === 'analytics' ? 'active' : ''}`}
            onClick={() => setActiveTab('analytics')}
          >
            Meal Analytics
          </button>
        </div>

        {activeTab === 'meals' && (
          <div className="dashboard-card">
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '20px' }}>
              <h2>Manage Meals</h2>
              <button
                className="btn-primary"
                onClick={() => setShowAddModal(true)}
                style={{
                  background: '#20C5AF',
                  color: 'white',
                  border: 'none',
                  padding: '10px 20px',
                  borderRadius: '4px',
                  cursor: 'pointer'
                }}
              >
                Add New Meal
              </button>
            </div>

            {/* Filters */}
            <div style={{ display: 'flex', gap: '15px', marginBottom: '20px' }}>
              <input
                type="text"
                placeholder="Search meals..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                style={{
                  padding: '8px 12px',
                  border: '1px solid #ddd',
                  borderRadius: '4px',
                  flex: 1
                }}
              />
              <select
                value={categoryFilter}
                onChange={(e) => setCategoryFilter(e.target.value)}
                style={{
                  padding: '8px 12px',
                  border: '1px solid #ddd',
                  borderRadius: '4px',
                  minWidth: '150px'
                }}
              >
                <option value="all">All Categories</option>
                {categories.map(category => (
                  <option key={category} value={category}>{category}</option>
                ))}
              </select>
            </div>

            {/* Meals Table */}
            <table className="admin-table">
              <thead>
                <tr>
                  <th>Name</th>
                  <th>Category</th>
                  <th>Prep Time</th>
                  <th>Cook Time</th>
                  <th>Servings</th>
                  <th>Dietary Tags</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {filteredMeals.map((meal) => (
                  <tr key={meal._id}>
                    <td>{meal.name}</td>
                    <td>{meal.category}</td>
                    <td>{meal.prepTime} min</td>
                    <td>{meal.cookTime} min</td>
                    <td>{meal.servings}</td>
                    <td>
                      {meal.dietaryTags && meal.dietaryTags.length > 0 
                        ? meal.dietaryTags.join(', ') 
                        : 'None'
                      }
                    </td>
                    <td>
                      <button
                        onClick={() => openEditModal(meal)}
                        style={{
                          background: '#007bff',
                          color: 'white',
                          border: 'none',
                          padding: '5px 10px',
                          borderRadius: '3px',
                          cursor: 'pointer',
                          marginRight: '5px'
                        }}
                      >
                        Edit
                      </button>
                      <button
                        onClick={() => handleDeleteMeal(meal._id)}
                        style={{
                          background: '#dc3545',
                          color: 'white',
                          border: 'none',
                          padding: '5px 10px',
                          borderRadius: '3px',
                          cursor: 'pointer'
                        }}
                      >
                        Delete
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>

            {filteredMeals.length === 0 && (
              <div style={{ textAlign: 'center', padding: '20px', color: '#666' }}>
                No meals found matching your criteria.
              </div>
            )}
          </div>
        )}

        {activeTab === 'analytics' && (
          <div className="dashboard-card">
            <h2>Meal Analytics</h2>
            <div className="stats-grid">
              <div className="stat-box">
                <h3>Total Meals</h3>
                <p className="stat-number">{meals.length}</p>
              </div>
              <div className="stat-box">
                <h3>Categories</h3>
                <p className="stat-number">{new Set(meals.map(m => m.category)).size}</p>
              </div>
              <div className="stat-box">
                <h3>Vegetarian Meals</h3>
                <p className="stat-number">
                  {meals.filter(m => m.dietaryTags && m.dietaryTags.includes('Vegetarian')).length}
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Add Meal Modal */}
        {showAddModal && (
          <div className="modal-overlay" onClick={() => setShowAddModal(false)}>
            <div className="modal-content" onClick={(e) => e.stopPropagation()}>
              <h3>Add New Meal</h3>
              <div className="modal-form">
                <input
                  type="text"
                  placeholder="Meal Name"
                  value={newMeal.name}
                  onChange={(e) => setNewMeal({...newMeal, name: e.target.value})}
                />
                <textarea
                  placeholder="Description"
                  value={newMeal.description}
                  onChange={(e) => setNewMeal({...newMeal, description: e.target.value})}
                />
                <select
                  value={newMeal.category}
                  onChange={(e) => setNewMeal({...newMeal, category: e.target.value})}
                >
                  <option value="">Select Category</option>
                  {categories.map(cat => (
                    <option key={cat} value={cat}>{cat}</option>
                  ))}
                </select>
                <textarea
                  placeholder="Ingredients (comma separated)"
                  value={newMeal.ingredients}
                  onChange={(e) => setNewMeal({...newMeal, ingredients: e.target.value})}
                />
                <textarea
                  placeholder="Instructions (one per line)"
                  value={newMeal.instructions}
                  onChange={(e) => setNewMeal({...newMeal, instructions: e.target.value})}
                />
                <div className="modal-buttons">
                  <button onClick={() => setShowAddModal(false)}>Cancel</button>
                  <button onClick={handleAddMeal}>Add Meal</button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Edit Meal Modal */}
        {showEditModal && selectedMeal && (
          <div className="modal-overlay" onClick={() => setShowEditModal(false)}>
            <div className="modal-content" onClick={(e) => e.stopPropagation()}>
              <h3>Edit Meal</h3>
              <div className="modal-form">
                <input
                  type="text"
                  placeholder="Meal Name"
                  value={selectedMeal.name}
                  onChange={(e) => setSelectedMeal({...selectedMeal, name: e.target.value})}
                />
                <textarea
                  placeholder="Description"
                  value={selectedMeal.description}
                  onChange={(e) => setSelectedMeal({...selectedMeal, description: e.target.value})}
                />
                <select
                  value={selectedMeal.category}
                  onChange={(e) => setSelectedMeal({...selectedMeal, category: e.target.value})}
                >
                  <option value="">Select Category</option>
                  {categories.map(cat => (
                    <option key={cat} value={cat}>{cat}</option>
                  ))}
                </select>
                <textarea
                  placeholder="Ingredients (comma separated)"
                  value={selectedMeal.ingredients}
                  onChange={(e) => setSelectedMeal({...selectedMeal, ingredients: e.target.value})}
                />
                <textarea
                  placeholder="Instructions (one per line)"
                  value={selectedMeal.instructions}
                  onChange={(e) => setSelectedMeal({...selectedMeal, instructions: e.target.value})}
                />
                <div className="modal-buttons">
                  <button onClick={() => setShowEditModal(false)}>Cancel</button>
                  <button onClick={handleEditMeal}>Update Meal</button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </Layout>
  );
}

export default MealManagement;
