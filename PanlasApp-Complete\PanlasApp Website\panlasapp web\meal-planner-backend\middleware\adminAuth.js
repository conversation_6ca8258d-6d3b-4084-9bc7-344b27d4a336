const User = require('../models/User');
const Admin = require('../models/Admin');

// Middleware to check if user is admin
const adminAuth = async (req, res, next) => {
  try {
    // First check if user is authenticated (should be done by auth middleware first)
    if (!req.user || !req.user.id) {
      return res.status(401).json({ message: 'Authentication required' });
    }

    // Get user from database to check admin status
    const user = await User.findById(req.user.id);
    if (!user) {
      return res.status(401).json({ message: 'User not found' });
    }

    // Check if user has admin or sub-admin flag
    if (!user.isAdmin && !user.isSubAdmin) {
      return res.status(403).json({ message: 'Access denied. Admin privileges required.' });
    }

    // Optionally check Admin collection for additional permissions
    const adminRecord = await Admin.findOne({ user: user._id, isActive: true });
    if (adminRecord) {
      req.adminRole = adminRecord.role;
      req.adminPermissions = adminRecord.permissions;
      
      // Update last activity
      adminRecord.lastActivity = new Date();
      await adminRecord.save();
    }

    // Add user info to request
    req.adminUser = user;
    next();
  } catch (error) {
    console.error('Admin auth error:', error);
    res.status(500).json({ message: 'Server error during admin authentication' });
  }
};

// Middleware to check specific admin permissions
const requirePermission = (permission) => {
  return (req, res, next) => {
    if (!req.adminPermissions || !req.adminPermissions.includes(permission)) {
      return res.status(403).json({ 
        message: `Access denied. Required permission: ${permission}` 
      });
    }
    next();
  };
};

// Middleware to check admin role level
const requireRole = (minRole) => {
  const roleHierarchy = {
    'moderator': 1,
    'sub_admin': 2,
    'admin': 3,
    'super_admin': 4
  };

  return (req, res, next) => {
    const userRoleLevel = roleHierarchy[req.adminRole] || 0;
    const requiredRoleLevel = roleHierarchy[minRole] || 0;

    if (userRoleLevel < requiredRoleLevel) {
      return res.status(403).json({ 
        message: `Access denied. Required role: ${minRole}` 
      });
    }
    next();
  };
};

// Middleware to check if user is sub-admin or higher
const subAdminAuth = async (req, res, next) => {
  try {
    // First check if user is authenticated (should be done by auth middleware first)
    if (!req.user || !req.user.id) {
      return res.status(401).json({ message: 'Authentication required' });
    }

    // Get user from database to check admin status
    const user = await User.findById(req.user.id);
    if (!user) {
      return res.status(401).json({ message: 'User not found' });
    }

    // Check if user has admin or sub-admin flag
    if (!user.isAdmin && !user.isSubAdmin) {
      return res.status(403).json({ message: 'Access denied. Sub-admin privileges required.' });
    }

    // Check Admin collection for additional permissions
    const adminRecord = await Admin.findOne({ user: user._id, isActive: true });
    if (adminRecord) {
      req.adminRole = adminRecord.role;
      req.adminPermissions = adminRecord.permissions;

      // Update last activity
      adminRecord.lastActivity = new Date();
      await adminRecord.save();
    }

    // Add user info to request
    req.adminUser = user;
    next();
  } catch (error) {
    console.error('Sub-admin auth error:', error);
    res.status(500).json({ message: 'Server error during sub-admin authentication' });
  }
};

// Middleware to check if user can perform write operations (admin only, not sub-admin)
const requireWriteAccess = (req, res, next) => {
  if (!req.adminUser || !req.adminUser.isAdmin) {
    return res.status(403).json({
      message: 'Access denied. Full admin privileges required for this operation.'
    });
  }
  next();
};

// Middleware to check if user can access meal management (admin or sub-admin)
const requireMealManagement = (req, res, next) => {
  if (!req.adminPermissions || !req.adminPermissions.includes('meal_management')) {
    // Allow if user is admin or sub-admin (they should have meal management access)
    if (req.adminUser && (req.adminUser.isAdmin || req.adminUser.isSubAdmin)) {
      return next();
    }
    return res.status(403).json({
      message: 'Access denied. Meal management privileges required.'
    });
  }
  next();
};

module.exports = {
  adminAuth,
  subAdminAuth,
  requirePermission,
  requireRole,
  requireWriteAccess,
  requireMealManagement
};
