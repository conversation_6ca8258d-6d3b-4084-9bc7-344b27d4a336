const express = require('express');
const router = express.Router();
const adminController = require('../controllers/adminController');
const auth = require('../middleware/auth');
const { adminAuth, subAdminAuth, requireWriteAccess, requireMealManagement } = require('../middleware/adminAuth');
const mealController = require('../controllers/mealController');

console.log('=== ADMIN ROUTES FILE LOADED ===');

// Apply auth middleware to all admin routes
router.use(auth);

// Get all users (admin and sub-admin can view)
router.get('/users', subAdminAuth, adminController.getAllUsers);

// User management routes (admin only for write operations)
console.log('Setting up user management routes...');
router.post('/users', adminAuth, requireWriteAccess, adminController.createUser);
router.put('/users/:userId', adminAuth, requireWriteAccess, adminController.updateUser);
console.log('Setting up disable/enable routes...');
router.put('/users/:userId/disable', adminAuth, requireWriteAccess, adminController.disableUser);
router.put('/users/:userId/enable', adminAuth, requireWriteAccess, adminController.enableUser);
console.log('Disable/enable routes set up');
router.put('/users/:userId/make-admin', adminAuth, requireWriteAccess, adminController.makeUserAdmin);
router.put('/users/:userId/remove-admin', adminAuth, requireWriteAccess, adminController.removeUserAdmin);
router.put('/users/:userId/make-sub-admin', adminAuth, requireWriteAccess, adminController.makeUserSubAdmin);
router.put('/users/:userId/remove-sub-admin', adminAuth, requireWriteAccess, adminController.removeUserSubAdmin);

// Get user signup statistics (admin and sub-admin can view)
router.get('/stats/signups', subAdminAuth, adminController.getUserSignupStats);

// Get system overview (admin and sub-admin can view)
router.get('/overview', subAdminAuth, adminController.getSystemOverview);

// Generate user registration report (admin and sub-admin can view)
router.get('/reports/registrations', subAdminAuth, adminController.getUserRegistrationReport);

// Get system health information (admin and sub-admin can view)
router.get('/system/health', subAdminAuth, adminController.getSystemHealth);

// Geolocation analytics (admin and sub-admin can view)
router.get('/analytics/geolocation', subAdminAuth, adminController.getGeolocationAnalytics);

// Meal management routes (accessible by admin and sub-admin)
console.log('Setting up meal management routes...');
router.get('/meals', subAdminAuth, requireMealManagement, mealController.getMeals);
router.get('/meals/:id', subAdminAuth, requireMealManagement, mealController.getFilipinoDishById);
router.post('/meals', subAdminAuth, requireMealManagement, mealController.createMeal);
router.put('/meals/:id', subAdminAuth, requireMealManagement, mealController.updateMeal);
router.delete('/meals/:id', subAdminAuth, requireMealManagement, mealController.deleteMeal);

console.log('=== ADMIN ROUTES SETUP COMPLETE ===');
module.exports = router;
